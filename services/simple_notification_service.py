"""
Упрощенный сервис для уведомлений о невыполненных домашних заданиях
"""
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Set, Tuple
from sqlalchemy import select, and_, func, text
from database.database import _get_session_maker
from database.models import (
    Curator, Student, Group, Homework, HomeworkResult, 
    Lesson, Subject, User
)
from database.repositories import NotificationRepository
from utils.config import HOMEWORK_DAYS_THRESHOLD


class SimpleHomeworkNotificationService:
    """Упрощенный сервис для работы с уведомлениями о домашних заданиях"""
    
    def __init__(self):
        self.notification_repo = NotificationRepository()
        self.logger = logging.getLogger(__name__)

    async def find_problem_students_simple(self, days_threshold: float = None) -> Dict[int, List[Dict]]:
        """
        Упрощенный поиск проблемных студентов через SQL запросы
        """
        if days_threshold is None:
            days_threshold = HOMEWORK_DAYS_THRESHOLD
            
        session_maker = _get_session_maker()
        async with session_maker() as session:
            cutoff_date = datetime.now() - timedelta(days=days_threshold)
            
            # SQL запрос для поиска проблемных студентов
            # ИСПРАВЛЕНО: ДЗ фильтруются только по предметам групп куратора
            sql = text("""
                SELECT DISTINCT
                    c.id as curator_id,
                    c.user_id as curator_user_id,
                    cu.name as curator_name,
                    cu.telegram_id as curator_telegram_id,
                    s.id as student_id,
                    s.user_id as student_user_id,
                    su.name as student_name,
                    su.telegram_id as student_telegram_id,
                    h.id as homework_id,
                    h.name as homework_name,
                    h.created_at as homework_created_at,
                    l.name as lesson_name,
                    subj.name as subject_name
                FROM curators c
                JOIN users cu ON c.user_id = cu.id
                JOIN curator_groups cg ON c.id = cg.curator_id
                JOIN groups g ON cg.group_id = g.id
                JOIN student_groups sg ON g.id = sg.group_id
                JOIN students s ON sg.student_id = s.id
                JOIN users su ON s.user_id = su.id
                JOIN subjects subj ON g.subject_id = subj.id
                JOIN homeworks h ON h.subject_id = g.subject_id  -- ИСПРАВЛЕНО: только предметы групп куратора
                JOIN lessons l ON h.lesson_id = l.id
                LEFT JOIN homework_results hr ON hr.student_id = s.id AND hr.homework_id = h.id
                WHERE h.created_at <= :cutoff_date
                AND hr.id IS NULL
                AND cu.role = 'curator'  -- Только кураторы, не админы
                ORDER BY c.id, s.id, h.created_at
            """)
            
            result = await session.execute(sql, {"cutoff_date": cutoff_date})
            rows = result.fetchall()
            
            # Группируем результаты по кураторам
            problem_students_by_curator = {}
            
            for row in rows:
                curator_id = row.curator_id
                student_id = row.student_id
                
                if curator_id not in problem_students_by_curator:
                    problem_students_by_curator[curator_id] = {}
                
                if student_id not in problem_students_by_curator[curator_id]:
                    problem_students_by_curator[curator_id][student_id] = {
                        'curator_name': row.curator_name,
                        'curator_telegram_id': row.curator_telegram_id,
                        'student_name': row.student_name,
                        'student_telegram_id': row.student_telegram_id,
                        'incomplete_homeworks': []
                    }
                
                problem_students_by_curator[curator_id][student_id]['incomplete_homeworks'].append({
                    'homework_id': row.homework_id,
                    'homework_name': row.homework_name,
                    'lesson_name': row.lesson_name,
                    'subject_name': row.subject_name,
                    'created_at': row.homework_created_at
                })
            
            # Преобразуем в нужный формат
            result_dict = {}
            for curator_id, students in problem_students_by_curator.items():
                result_dict[curator_id] = list(students.values())
            
            return result_dict

    async def find_new_problem_students_simple(self, days_threshold: float = None) -> Dict[int, List[Dict]]:
        """
        Найти НОВЫХ проблемных студентов (которым еще не отправляли уведомления)
        """
        all_problems = await self.find_problem_students_simple(days_threshold)
        new_problems = {}
        
        for curator_id, student_problems in all_problems.items():
            new_student_problems = []
            
            for problem_data in student_problems:
                # Извлекаем student_id из telegram_id (упрощение)
                student_telegram_id = problem_data['student_telegram_id']
                
                # Проверяем, было ли недавнее уведомление о новой проблеме
                # Для упрощения используем telegram_id как student_id
                has_recent = await self.notification_repo.has_recent_notification(
                    curator_id=curator_id,
                    student_id=student_telegram_id,  # Используем telegram_id как ID
                    notification_type='new_problem',
                    hours=24
                )
                
                if not has_recent:
                    new_student_problems.append(problem_data)
            
            if new_student_problems:
                new_problems[curator_id] = new_student_problems
        
        return new_problems

    def format_homework_message_simple(self, student_data: Dict) -> str:
        """
        Форматировать сообщение о невыполненных ДЗ студента
        """
        student_name = student_data['student_name']
        student_telegram_id = student_data['student_telegram_id']
        incomplete_homeworks = student_data['incomplete_homeworks']

        # Заголовок с информацией о студенте
        message = f"👤 <b>{student_name}</b>\n"
        message += f"📱 @{student_telegram_id}\n\n"

        # Группируем ДЗ по предметам
        homeworks_by_subject = {}
        for homework in incomplete_homeworks:
            subject_name = homework['subject_name']
            if subject_name not in homeworks_by_subject:
                homeworks_by_subject[subject_name] = []
            homeworks_by_subject[subject_name].append(homework)

        # Формируем сообщение по предметам
        message += "📚 <b>Невыполненные домашние задания:</b>\n\n"

        for subject_name, homeworks in homeworks_by_subject.items():
            message += f"📖 <b>{subject_name}:</b>\n"

            for i, homework in enumerate(homeworks, 1):
                days_overdue = (datetime.now() - homework['created_at']).days
                message += f"   {i}. <b>{homework['lesson_name']}</b> - {homework['homework_name']}\n"
                message += f"      📅 Создано: {homework['created_at'].strftime('%d.%m.%Y')}"
                message += f" ({days_overdue} дн. назад)\n"

            message += "\n"

        return message

    async def send_notification_to_curator_simple(self, bot, curator_id: int, student_data: Dict, 
                                                 notification_type: str) -> bool:
        """
        Отправить уведомление куратору о проблемном студенте
        """
        try:
            curator_telegram_id = student_data.get('curator_telegram_id')
            if not curator_telegram_id:
                self.logger.error(f"Не найден telegram_id куратора {curator_id}")
                return False
            
            # Формируем сообщение
            message = self.format_homework_message_simple(student_data)
            
            # Добавляем заголовок в зависимости от типа уведомления
            if notification_type == 'new_problem':
                header = "🚨 <b>НОВЫЙ ПРОБЛЕМНЫЙ СТУДЕНТ</b>\n\n"
            else:  # daily_summary
                header = "📊 <b>ЕЖЕДНЕВНАЯ СВОДКА</b>\n\n"
            
            full_message = header + message
            
            # Отправляем сообщение
            await bot.send_message(
                chat_id=curator_telegram_id,
                text=full_message,
                parse_mode='HTML'
            )
            
            # Логируем отправку (упрощенно)
            homework_ids = [hw['homework_id'] for hw in student_data['incomplete_homeworks']]
            await self.notification_repo.create_notification_log(
                curator_id=curator_id,
                student_id=student_data['student_telegram_id'],  # Используем telegram_id
                notification_type=notification_type,
                homework_ids=homework_ids
            )
            
            self.logger.info(
                f"Отправлено уведомление куратору {student_data.get('curator_name', curator_id)} "
                f"о студенте {student_data['student_name']}"
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Ошибка отправки уведомления куратору {curator_id}: {e}")
            return False

    async def process_new_problems(self, bot) -> int:
        """
        Обработать новых проблемных студентов (отправить уведомления)
        """
        new_problems = await self.find_new_problem_students_simple()
        notifications_sent = 0
        
        for curator_id, student_problems in new_problems.items():
            for student_data in student_problems:
                success = await self.send_notification_to_curator_simple(
                    bot=bot,
                    curator_id=curator_id,
                    student_data=student_data,
                    notification_type='new_problem'
                )
                if success:
                    notifications_sent += 1
        
        self.logger.info(f"Обработка новых проблем завершена. Отправлено уведомлений: {notifications_sent}")
        return notifications_sent

    async def process_daily_summary(self, bot) -> int:
        """
        Обработать ежедневную сводку (отправить всем кураторам)
        """
        all_problems = await self.find_problem_students_simple()
        notifications_sent = 0
        
        for curator_id, student_problems in all_problems.items():
            for student_data in student_problems:
                success = await self.send_notification_to_curator_simple(
                    bot=bot,
                    curator_id=curator_id,
                    student_data=student_data,
                    notification_type='daily_summary'
                )
                if success:
                    notifications_sent += 1
        
        self.logger.info(f"Ежедневная сводка завершена. Отправлено уведомлений: {notifications_sent}")
        return notifications_sent
